---
title: LobeChat Authentication Service Configuration
description: >-
  Learn how to configure external authentication services using <PERSON> or <PERSON> Auth for centralized user authorization management. Supported authentication services include Auth0, Azure ID, etc.

tags:
  - Authentication Service
  - Next Auth
  - SSO
  - Clerk
---

# Authentication Service

LobeChat supports the configuration of external authentication services using <PERSON> or <PERSON> Auth for internal use within enterprises/organizations to centrally manage user authorization.

## Clerk

Clerk is a comprehensive identity verification solution that has recently gained popularity. It provides a simple yet powerful API and services to handle user authentication and session management. Clerk's design philosophy is to offer a concise and modern authentication solution that enables developers to easily integrate and use it.

LobeChat has deeply integrated with <PERSON> to provide users with a more secure and convenient login and registration experience. It also relieves developers from the burden of managing authentication logic. Clerk's concise and modern design philosophy aligns perfectly with LobeChat's goals, making user management on the entire platform more efficient and reliable.

By setting the environment variables `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY` in LobeChat's environment, you can enable and use <PERSON>.

## Next Auth

Before using NextAuth, please set the following variables in LobeChat's environment variables:

| Environment Variable           | Type     | Description                                                                                                                                                                                                                                |
| ------------------------------ | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `NEXT_PUBLIC_ENABLE_NEXT_AUTH` | Required | This is used to enable the NextAuth service. Set it to `1` to enable it; changing this setting requires recompiling the application. Users deploying with the `lobehub/lobe-chat-database` image have this configuration added by default. |
| `NEXT_AUTH_SECRET`             | Required | The key used to encrypt Auth.js session tokens. You can use the following command: `openssl rand -base64 32`, or visit `https://generate-secret.vercel.app/32` to generate the key.                                                        |
| `NEXTAUTH_URL`                 | Required | This URL specifies the callback address for Auth.js when performing OAuth verification. Set this only if the default generated redirect address is incorrect. `https://example.com/api/auth`                                               |
| `NEXT_AUTH_SSO_PROVIDERS`      | Optional | This environment variable is used to enable multiple identity verification sources simultaneously, separated by commas, for example, `auth0,microsoft-entra-id,authentik`.                                                                 |

Currently supported identity verification services include:

<Cards>
  <Card href={'/docs/self-hosting/advanced/auth/next-auth/auth0'} title={'Auth0'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/microsoft-entra-id'} title={'Microsoft Entra ID'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/authentik'} title={'Authentik'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/github'} title={'Github'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/zitadel'} title={'ZITADEL'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/cloudflare-zero-trust'} title={'Cloudflare Zero Trust'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/authelia'} title={'Authelia'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/logto'} title={'Logto'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/keycloak'} title={'Keycloak'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/google'} title={'Google'} />

  <Card href={'/docs/self-hosting/advanced/auth/next-auth/okta'} title={'Okta'} />
</Cards>

Click on the links to view the corresponding platform's configuration documentation.

## Advanced Configuration

To simultaneously enable multiple identity verification sources, please set the `NEXT_AUTH_SSO_PROVIDERS` environment variable, separating them with commas, for example, `auth0,microsoft-entra-id,authentik`.

The order corresponds to the display order of the SSO providers.

| SSO Provider          | Value                   |
| --------------------- | ----------------------- |
| Auth0                 | `auth0`                 |
| Authenlia             | `authenlia`             |
| Authentik             | `authentik`             |
| Casdoor               | `casdoor`               |
| Cloudflare Zero Trust | `cloudflare-zero-trust` |
| Github                | `github`                |
| Logto                 | `logto`                 |
| Microsoft Entra ID    | `microsoft-entra-id`    |
| ZITADEL               | `zitadel`               |
| Keycloak              | `keycloak`              |
| Google                | `google`                |
| Okta                  | `okta`                  |

## Other SSO Providers

Please refer to the [NextAuth.js](https://next-auth.js.org/providers) documentation and feel free to submit a Pull Request.
