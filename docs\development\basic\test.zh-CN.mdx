# 测试指南

LobeChat 的测试策略包括单元测试和端到端 (E2E) 测试。下面是每种测试的详细说明：

## 单元测试

单元测试用于测试应用中的独立单元（如组件、函数、工具函数等）的功能。我们使用 [vitest][vitest-url] 进行单元测试。

要运行单元测试，可以使用以下命令：

```bash
npm run test
```

这将运行所有的单元测试，并生成测试报告。

我们鼓励开发者在编写代码时，同时编写对应的单元测试，以确保代码的质量和稳定性。

## 🚧 端到端测试

端到端测试用于测试应用在真实环境中的功能和性能。它模拟用户的真实操作，并验证应用在不同场景下的表现。

在 LobeChat 中，目前暂时没有集成端到端测试，我们会在后续迭代中逐步引入端到端测试。

## 开发测试

### 1. 单元测试

单元测试是针对应用中的最小可测试单元进行的测试，通常是针对函数、组件或模块进行的测试。在 LobeChat 中，我们使用 [vitest][vitest-url] 进行单元测试。

#### 编写测试用例

在编写单元测试之前，您需要创建一个与被测试文件相同的目录，并将测试文件命名为 `<filename>.test.ts`。例如，如果要测试 `src/utils/formatDate.ts` 文件，测试文件应命名为 `src/utils/formatDate.test.ts`。

在测试文件中，您可以使用 `describe` 和 `it` 函数来组织和编写测试用例。`describe` 函数用于创建测试套件，`it` 函数用于编写具体的测试用例。

```ts
import { formatNumber } from './formatNumber';

describe('formatNumber', () => {
  it('should format number with comma separator', () => {
    const result = formatNumber(1000);
    expect(result).toBe('1,000');
  });

  it('should return the same number if it is less than 1000', () => {
    const result = formatNumber(500);
    expect(result).toBe('500');
  });
});
```

在测试用例中，您可以使用 `expect` 函数来断言测试结果是否符合预期。`expect` 函数可以与各种匹配器（matchers）一起使用，例如 `toBe`、`toEqual`、`toBeTruthy` 等。

#### 运行单元测试

通过运行以下命令来执行单元测试：

```bash
npm run test
```

这将运行所有的单元测试，并输出测试结果。

## 测试策略

为了编写有效的测试用例，您可以考虑以下测试策略：

- **边界条件测试**：测试输入的边界条件，例如最小值、最大值、空值等。
- **异常情况测试**：测试处理异常情况的代码，例如错误处理、异常情况下的回退等。
- **功能测试**：测试应用的各个功能模块是否正常工作，包括用户交互、数据处理等。
- **兼容性测试**：测试应用在不同浏览器和设备上的兼容性。
- **性能测试**：测试应用在不同负载下的性能表现，例如响应时间、资源占用等。

同时，请确保您的测试用例具有良好的覆盖率，覆盖到应用中的关键代码和功能。

通过合理编写和执行单元测试、集成测试和端到端测试，您可以提高应用的质量和稳定性，并及时发现和修复潜在的问题。

[vitest-url]: https://vitest.dev/
