---
title: LobeChat Database Docker Image Official Release
description: >-
  LobeChat v1.8.0 launches the official database Docker image, supporting cloud data synchronization and user management, along with comprehensive self-deployment documentation.

tags:
  - LobeChat
  - Docker Image
  - Cloud Deployment
  - Database
  - Postgres
---

# LobeChat Database Docker Image: The Final Piece of the Cloud Deployment Puzzle

We are excited to announce the official release of the long-awaited database Docker image for LobeChat v1.8.0! This marks a significant milestone in our server database offerings, providing users with a complete cloud deployment solution.

## 🚀 Core Features

- **Lightweight Deployment**: The Docker image is only 90MB, yet offers full database functionality.
- **Optimized Performance**: Pre-configured with Server Postgres and NextAuth authentication system to ensure optimal connectivity performance.
- **Cloud Synchronization**: Enjoy a seamless cloud data synchronization experience right after deployment.
- **Flexible Authentication**: Supports integration with third-party SSO service providers like Auth0.

## 📘 Upgraded Deployment Documentation

To ensure users can complete the deployment smoothly, we have optimized the structure of our deployment documentation:

- Clear introduction to the framework concepts
- Detailed deployment case studies
- Comprehensive self-deployment operation guide

You can start deploying your own LobeChat service by visiting the [official documentation](https://lobehub.com/en/docs/self-hosting/server-database).

## 🔮 Future Outlook

Our knowledge base feature is also in development, so stay tuned for more exciting updates!

This update marks a significant breakthrough for LobeChat in cloud deployment solutions, making private deployment easier than ever. We appreciate the community's patience, and we will continue to strive to provide users with a better experience.
