name: Daily i18n Update

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

# Add permissions configuration
permissions:
  contents: write
  pull-requests: write

jobs:
  update-i18n:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: Configure Git
        run: |
          git config --global user.name "lobehubbot"
          git config --global user.email "<EMAIL>"

      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Install bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: ${{ secrets.BUN_VERSION }}

      - name: Install deps
        run: bun i

      - name: Update i18n
        run: bun run i18n
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENAI_PROXY_URL: ${{ secrets.OPENAI_PROXY_URL }}

      - name: create pull request
        id: cpr
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GH_TOKEN }}
          add-paths: |
            locales/**/*.json
          labels: |
            i18n
            automated
            style
          branch: style/auto-i18n
          delete-branch: true
          title: '🤖 style: update i18n'
          commit-message: '💄 style: update i18n'
          body: |
            This PR was automatically generated by the i18n update workflow.
            Please review the changes and merge if everything looks good.

            ## 🤖 Automation Info
              - Workflow: `${{ github.workflow }}`
              - Run ID: `${{ github.run_id }}`
              - Commit: `${{ github.sha }}`

            <details>
            <summary>i18n Update Log</summary>
            ```bash
            $(cat i18n_update.log)
            ```
            </details>
