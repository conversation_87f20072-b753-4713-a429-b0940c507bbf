---
title: LobeChat 1.0：新的架构与新的可能
description: >-
  LobeChat 1.0 带来了服务端数据库、用户鉴权管理的全新架构与特性，开启了新的可能 。在此基础上， LobeChat Cloud 开启 Beta 版测试。

tags:
  - LobeChat
  - 服务端数据库
  - 用户鉴权
  - Beta 测试
---

# LobeChat 1.0：新的架构与新的可能

自从 3 月份宣布迈向 1.0 ，我们就开始着手全方面的升级。经过 2 个月的密集研发，我们很高兴地宣布 LobeChat 1.0 正式发布了！一起来看看我们的全新样貌吧～

## 服务端数据库支持

在 LobeChat 1.0 中，最大的特性是支持了服务端数据库。在 0.x 时代，由于缺乏服务端持久化存储，许多用户迫切需要的功能实现困难，或完全无法实现，例如知识库、跨端同步、私有助手市场等等。

## 用户鉴权管理

在 0.x 时代，和服务端数据库搭配的呼声最高的特性就是用户鉴权管理。在此之前，我们已经接入了 next-auth 和 clerk 作为鉴权解决方案。并针对多用户管理的诉求，将设置界面重构为了用户面板，在新的用户面板中整合了相关的用户信息。

## LobeChat Cloud 开启 Beta 测试

LobeChat Cloud 是我们基于 LobeChat 开源版的商业化版本，上述 1.0 的功能在 LobeChat Cloud 中均已上线，目前已开启 Beta 测试。如果你感兴趣，可以在这里加入我们的 waitlist ， Beta 测试期间每天都会发放体验名额。
