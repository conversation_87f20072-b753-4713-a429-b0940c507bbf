---
title: LobeChat support Progressive Web Apps (PWA)
description: >-
  Discover how LobeChat utilizes Progressive Web App (PWA) technology to provide a seamless and near-native app experience on both desktop and mobile devices. Learn how to install LobeChat as a desktop app for enhanced convenience.

tags:
  - Progressive Web App
  - PWA
  - LobeChat
  - Web Applications
  - User Experience
---

# Progressive Web App (PWA)

<Image alt={'Progressive Web App (PWA)'} borderless cover src={'https://github.com/user-attachments/assets/9647f70f-b71b-43b6-9564-7cdd12d1c24d'} />

We understand the importance of providing a seamless experience for users in today's multi-device environment. To achieve this, we have adopted Progressive Web App [PWA](https://support.google.com/chrome/answer/9658361) technology, which is a modern web technology that elevates web applications to a near-native app experience. Through PWA, LobeChat is able to provide a highly optimized user experience on both desktop and mobile devices, while maintaining lightweight and high performance characteristics. Visually and perceptually, we have also carefully designed it to ensure that its interface is indistinguishable from a native app, providing smooth animations, responsive layouts, and adaptation to different screen resolutions of various devices.

If you are unfamiliar with the installation process of PWA, you can follow the steps below to add LobeChat as a desktop app (also applicable to mobile devices):

## Running on Chrome / Edge

<Callout type={'important'}>
  On macOS, when using a Chrome-installed PWA, it is required that Chrome be open, otherwise Chrome
  will automatically open and then launch the PWA app.
</Callout>

<Steps>
  ### Run Chrome or Edge browser on your computer

  ### Visit the LobeChat webpage

  ### In the top right corner of the address bar, click the <kbd>Install</kbd> icon

  ### Follow the on-screen instructions to complete the PWA installation
</Steps>

## Running on Safari

Safari PWA requires macOS Ventura or later. The PWA installed by Safari does not require Safari to be open; you can directly open the PWA app.

<Steps>
  ### Run Safari browser on your computer

  ### Visit the LobeChat webpage

  ### In the top right corner of the address bar, click the <kbd>Share</kbd> icon

  ### Click <kbd>Add to Dock</kbd>

  ### Follow the on-screen instructions to complete the PWA installation
</Steps>

<Callout type={'tip'}>
  The default installed LobeChat PWA icon has a black background, you can use <kbd>cmd</kbd> +{' '}
  <kbd>i</kbd> to paste the following image to replace it with a white background.
</Callout>

<Image alt={'PWA White Icon'} borderless cover src={'https://github.com/lobehub/lobe-chat/assets/36695271/16ce82cb-49be-4d4d-ac86-4403a1536917'} />
