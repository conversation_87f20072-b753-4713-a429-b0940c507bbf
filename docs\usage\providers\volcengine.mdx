---
title: Using the Volcano Engine API Key in LobeChat
description: Learn how to configure and use the Volcano Engine AI model in LobeChat, obtain API keys, and start conversations.
tags:
  - LobeChat
  - Volcengine
  - Doubao
  - API Key
  - Web UI
---

# Using Volcengine in LobeChat

<Image alt={'Using Volcengine in LobeChat'} cover src={'https://github.com/user-attachments/assets/b9da065e-f964-44f2-8260-59e182be2729'} />

[Volcengine](https://www.volcengine.com/) is a cloud service platform under ByteDance that provides large language model (LLM) services through "Volcano Ark," supporting multiple mainstream models such as Baichuan Intelligent, Mobvoi, and more.

This document will guide you on how to use Volcengine in LobeChat:

<Steps>
  ### Step 1: Obtain the Volcengine API Key

  - First, visit the [Volcengine official website](https://www.volcengine.com/) and complete the registration and login process.
  - Access the Volcengine console and navigate to [Volcano Ark](https://console.volcengine.com/ark/).

  <Image alt={'Entering Volcano Ark API Management Page'} inStep src={'https://github.com/user-attachments/assets/d6ace96f-0398-4847-83e1-75c3004a0e8b'} />

  - Go to the `API Key Management` menu and click `Create API Key`.
  - Copy and save the created API Key.

  ### Step 2: Configure Volcengine in LobeChat

  - Navigate to the `Application Settings` page in LobeChat and select `AI Service Providers`.
  - Find the `Volcengine` option in the provider list.

  <Image alt={'Entering Volcengine API Key'} inStep src={'https://github.com/user-attachments/assets/237864d6-cc5d-4fe4-8a2b-c278016855c5'} />

  - Open the Volcengine service provider and enter the obtained API Key.
  - Choose a Volcengine model for your assistant to start the conversation.

  <Image alt={'Selecting a Volcengine Model'} inStep src={'https://github.com/user-attachments/assets/702c191f-8250-4462-aed7-accb18b18dea'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, so please refer to Volcengine's
    pricing policy.
  </Callout>
</Steps>

You can now use the models provided by Volcengine for conversations in LobeChat.
