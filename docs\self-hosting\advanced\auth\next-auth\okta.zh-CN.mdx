---
title: 在 LobeChat 中配置 Okta 身份验证服务 - 详细步骤和环境变量设置
description: >-
  学习如何在 LobeChat 中为您的组织配置 Okta 身份验证服务，包括创建应用程序、添加用户和配置环境变量等。

tags:
  - Okta
  - 身份验证
  - 单点登录
  - 环境变量
  - 用户管理
  - SSO 集成
  - 社交登录
---

# 配置 Okta 身份验证服务

<Steps>
  ### 创建 Okta 应用程序

  注册并登录 [Okta][okta-client-page]，打开左侧导航栏中的「Applications」子选项卡，点击「Applications」切换到应用程序管理界面。点击左上角的「Create App Integration」创建应用程序。

  在登录方法中选择「OIDC - OpenID Connect」，然后在应用程序类型中选择「Web Application」。

  填写以下设置：

  | 设置名称                   | 描述                                          | 示例信息                                          |
  | ---------------------- | ------------------------------------------- | --------------------------------------------- |
  | App Integration Name   | 您的用户将看到的应用程序名称                              | LobeChat Instance                             |
  | Sign-in redirect URIs  | Okta 将用户登录请求的身份验证响应和 ID 令牌发送到这些 URI         | (http(s)://your-domain/api/auth/callback/okta |
  | Sign-out redirect URIs | 您的应用程序联系 Okta 关闭用户会话后，Okta 将用户重定向到这些 URI 之一 | (http(s)://your-domain                        |

  <Callout type={'important'}>
    您可以在部署后填写或修改所有字段，但请确保填写的 URL 与部署的 URL 一致。
  </Callout>

  ### 添加用户

  点击顶部导航栏中的「Assignments」进入用户管理界面，您可以在此创建或分配组织中的用户来登录 LobeChat。

  ### 配置环境变量

  在部署 LobeChat 时，您需要配置以下环境变量：

  | 环境变量                      | 类型 | 描述                                                                                   |
  | ------------------------- | -- | ------------------------------------------------------------------------------------ |
  | `NEXT_AUTH_SECRET`        | 必选 | 用于加密 Auth.js 会话令牌的密钥。您可以使用以下命令生成密钥：`openssl rand -base64 32`                         |
  | `NEXT_AUTH_SSO_PROVIDERS` | 必选 | 选择 LoboChat 的单点登录提供商。使用 Okta 请填写 `okta`。                                             |
  | `AUTH_OKTA_ID`            | 必选 | Okta 应用程序的客户端 ID                                                                     |
  | `AUTH_OKTA_SECRET`        | 必选 | Okta 应用程序的客户端密钥                                                                      |
  | `AUTH_OKTA_ISSUER`        | 必选 | Okta 应用程序的域名，`https://example.oktapreview.com`                                       |
  | `NEXTAUTH_URL`            | 可选 | 该 URL 用于指定 Auth.js 在执行 OAuth 认证时的回调地址。仅当默认地址不正确时才需要设置。`https://example.com/api/auth` |

  <Callout type={'tip'}>
    您可以在 [📘环境变量](/zh/docs/self-hosting/environment-variables/auth#okta) 查阅相关变量详情。
  </Callout>
</Steps>

<Callout>
  部署成功后，用户将能够使用在 Okta 中配置的用户进行身份验证并使用 LobeChat。
</Callout>

[okta-client-page]: https://login.okta.com
