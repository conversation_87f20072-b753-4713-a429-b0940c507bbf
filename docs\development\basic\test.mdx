# Testing Guide

LobeChat's testing strategy includes unit testing and end-to-end (E2E) testing. Below are detailed explanations of each type of testing:

## Unit Testing

Unit testing is used to test the functionality of independent units in the application, such as components, functions, utility functions, etc. We use [vitest][vitest-url] for unit testing.

To run unit tests, you can use the following command:

```bash
npm run test
```

This will run all unit tests and generate a test report.

We encourage developers to write corresponding unit tests while writing code to ensure the quality and stability of the code.

## 🚧 End-to-End Testing

End-to-end testing is used to test the functionality and performance of the application in a real environment. It simulates real user operations and verifies the application's performance in different scenarios.

Currently, there is no integrated end-to-end testing in LobeChat. We will gradually introduce end-to-end testing in subsequent iterations.

## Development Testing

### 1. Unit Testing

Unit testing is conducted on the smallest testable units in the application, usually functions, components, or modules. In LobeChat, we use [vitest][vitest-url] for unit testing.

#### Writing Test Cases

Before writing unit tests, you need to create a directory with the same name as the file to be tested and name the test file `<filename>.test.ts`. For example, if you want to test the `src/utils/formatDate.ts` file, the test file should be named `src/utils/formatDate.test.ts`.

In the test file, you can use the `describe` and `it` functions to organize and write test cases. The `describe` function is used to create a test suite, and the `it` function is used to write specific test cases.

```ts
import { formatNumber } from './formatNumber';

describe('formatNumber', () => {
  it('should format number with comma separator', () => {
    const result = formatNumber(1000);
    expect(result).toBe('1,000');
  });

  it('should return the same number if it is less than 1000', () => {
    const result = formatNumber(500);
    expect(result).toBe('500');
  });
});
```

In test cases, you can use the `expect` function to assert whether the test results meet expectations. The `expect` function can be used with various matchers, such as `toBe`, `toEqual`, `toBeTruthy`, etc.

#### Running Unit Tests

Execute unit tests by running the following command:

```bash
npm run test
```

This will run all unit tests and output the test results.

## Testing Strategy

To write effective test cases, you can consider the following testing strategies:

- **Boundary Testing**: Test the boundary conditions of inputs, such as minimum value, maximum value, empty value, etc.
- **Exception Testing**: Test the code handling exceptional cases, such as error handling, fallback in exceptional situations, etc.
- **Functional Testing**: Test whether various functional modules of the application work properly, including user interaction, data processing, etc.
- **Compatibility Testing**: Test the compatibility of the application on different browsers and devices.
- **Performance Testing**: Test the performance of the application under different loads, such as response time, resource utilization, etc.

Also, ensure that your test cases have good coverage, covering critical code and functionality in the application.

By properly writing and executing unit tests, integration tests, and end-to-end tests, you can improve the quality and stability of the application and promptly identify and fix potential issues.

[vitest-url]: https://vitest.dev/
